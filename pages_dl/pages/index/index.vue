<template>
  <view class="wrapper">
    <u-navbar title="任务列表" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder @leftClick="backHome">
      <template #left>
        <u-icon name="home" size="40rpx" color="#fff"></u-icon>
      </template>
    </u-navbar>

    <view class="content">
      <view class="empty-wrapper" v-if="emptyVisible">
        <u-empty text="当前用户未分配任务, 请联系管理员" icon="/static/images/empty.png"></u-empty>
      </view>
      <template>
        <view class="content-group">
          <view class="content-group-title">氮磷流失调查</view>
          <view class="task-item" v-for="(task, index) in taskList" v-if="isSampling(task)" :key="index" @click="taskClickHandler(task)">
            <view class="task-item-title-wrapper">
              <u-icon name="calendar-fill" color="#FF7A45" size="80rpx"></u-icon>
              <view class="task-item-title">{{ task.name }}</view>
            </view>
            <view class="task-item-info">
              <view class="task-item-info-content" v-for="item in taskItemInfo" :key="item.key">
                {{ `${item.label}: ${item.key === "create_time" ? dateFormat.format(task[item.key]) : task[item.key]}` }}
              </view>
            </view>
            <view class="task-item-operation">
              <view class="btn-task-item" v-for="btn in getTaskButtonList(task)" :key="btn.key" @tap.stop="fillOutFormHandler(task, btn.key)">
                <u-button
                  :disabled="btn.disabled"
                  :icon="btn.icon"
                  :type="btn.type"
                  :text="btn.label"
                  :color="btn.color"
                  shape="circle"
                  plain
                ></u-button>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <Tabbar :config="tabbar" :value="0"></Tabbar>
  </view>
</template>

<script>
// 权限白名单
import Tabbar from "@/components/common/Tabbar";
import { tabbar } from "@/pages_dl/config";

import BasePage from "@/pages/BasePage";
import TabPage from "@/pages/TabPage";
import { constants } from "@/pages_dl/constants";
import dateFormat from "@/utils/date.js";

import loginApi from "@/apis/login";
import formDataApi from "@/apis/formData";
import taskApi from "@/pages_dl/apis/task";

const ROLE_WHITE = ["dl_county_collection", "admin"];

const TASK_ITEM_INFO = [
  { label: "年份", key: "年份" },
  { label: "开始日期", key: "create_time" },
];

const TASK_STATUS = ["未开始", "进行中", "已结束"];

export default {
  mixins: [BasePage, TabPage],

  components: {
    Tabbar,
  },

  data() {
    return {
      taskList: [],
      roleWhite: ROLE_WHITE,
      taskItemInfo: TASK_ITEM_INFO,
      emptyVisible: false,
      menuName: null,

      dateFormat,
      tabbar,
    };
  },

  async onLoad(options) {
    this.menuName = options.menuName;
    try {
      await this.getUserInfo();
      // 请求任务表
      await this.getTaskList();

      // 获取系统配置表并缓存起来
      await this.getSystemConfigForm();
    } catch (err) {
      console.log(err);
    }
  },

  methods: {
    backHome() {
      uni.reLaunch({
        url: "/pages/Workspace/index",
      });
    },
    async getUserInfo() {
      try {
        const userInfo = await loginApi.getNewUserInfo();
        await uni.setStorage({
          key: "userInfo",
          data: JSON.stringify(userInfo),
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 获取系统配置表
    async getSystemConfigForm() {
      try {
        const config = await formDataApi.getFormRecords("系统配置表");
        const obj = {};
        config.list.forEach((item) => {
          obj[item.配置项] = item.配置值;
        });
        uni.setStorageSync("systemConfig", JSON.stringify(obj));
      } catch (error) {
        console.log(error);
      }
    },
    // 判断是否是 地膜单位采样员
    isSampling(f) {
      const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const { roles } = userInfo;
      let isSamplingUnit = false;
      roles.forEach((item) => {
        if (item.code === "dm_samplingunit_sampling") {
          isSamplingUnit = true;
        }
      });
      return isSamplingUnit ? f.任务类型 === "地膜监测" : true;
    },
    async getTaskList() {
      const filter = ["and", ["=", "业务类型", "dl"], ["=", "status", "进行中"]];
      const result = await taskApi.getTaskList(filter);
      if (!result) {
        return;
      }
      this.taskList = result;
      this.emptyVisible = this.taskList.length === 0;
    },

    getTaskButtonList(task) {
      const { 业务类型 } = task;
      console.log("业务类型", 业务类型);
      const { status } = task;
      if (!status) return;
      //  未开始，进行中，已结束
      switch (status) {
        case TASK_STATUS[0]: {
          return [
            {
              label: "填报",
              key: "add",
              disabled: true,
              icon: "edit-pen",
              type: "primary",
              color: this.$constants.COLOR.PRIMARY_COLOR,
            },
            {
              label: "查看",
              key: "readonly",
              disabled: true,
              icon: "file-text",
              type: "info",
            },
          ];
        }
        case TASK_STATUS[1]: {
          return [
            {
              label: "填报",
              key: "add",
              disabled: false,
              icon: "edit-pen",
              type: "primary",
              color: this.$constants.COLOR.PRIMARY_COLOR,
            },
            {
              label: "查看",
              key: "readonly",
              disabled: false,
              icon: "file-text",
              type: "info",
            },
          ];
        }
        case TASK_STATUS[2]: {
          return [
            {
              label: "填报",
              key: "add",
              disabled: true,
              icon: "edit-pen",
              type: "primary",
              color: this.$constants.COLOR.PRIMARY_COLOR,
            },
            {
              label: "查看",
              key: "readonly",
              disabled: false,
              icon: "file-text",
              type: "info",
            },
          ];
        }
      }
    },

    taskClickHandler(task) {
      const { 调查表_uid, _id, 年份 } = task;
      const pageUrl = `${constants.PAGE.FORM_LIST_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
      console.log("pageUrl", pageUrl);

      this.navigateTo(pageUrl);
    },

    navigatePointListHandler(task, taskType = null) {
      const { 子任务表_uid, _id, 任务类型, 业务类型, 年份 } = task;
      const menuName = "氮磷流失调查";
      let pageUrl;
      pageUrl = `${constants.PAGE.POINT_LIST_URL}?子任务表_uid=${子任务表_uid}&年份=${年份}&任务_uid=${_id}&任务类型=${任务类型}&menuName=${menuName}`;
      this.navigateTo(pageUrl);
    },

    fillOutFormHandler(task, key) {
      if (!key) return;
      switch (key) {
        case "add": {
          const { status } = task;
          if (status === TASK_STATUS[1]) {
            const { 调查表_uid, _id, 年份 } = task;
            const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
            console.log("pageUrl", pageUrl);

            this.navigateTo(pageUrl);
            break;
          }
        }
        case "readonly": {
          this.taskClickHandler(task);
          break;
        }
        case "goTo": {
          this.navigatePointListHandler(task);
          break;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_dl/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }
}

.wrapper {
  position: fixed;
  width: calc(100% - 40rpx);
  height: 100vh;
  background: #f5f5f9;
  padding: 20rpx;

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 120rpx);
    overflow: auto;

    .content-group {
      .content-group-title {
        padding: 20rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #010101;
        line-height: 45rpx;
      }

      .task-item {
        padding: 8rpx;
        margin: 0 5rpx 20rpx 5rpx;
        border-radius: 20rpx;
        border: solid 1px #eee;
        box-shadow: 0 0 3px 2px #eee;
        width: calc(100% - 28rpx);
        max-height: 400rpx;
        background: #fff;
        display: flex;
        flex-direction: column;

        .task-item-info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          flex-wrap: nowrap;
          padding: 0 20rpx;
          font-weight: normal;

          .task-item-info-content {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #010101;
            line-height: 40rpx;
            padding: 18rpx;
            // margin-right: 80rpx;
          }
        }

        .task-item-operation {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          height: 64rpx;
          margin: 30rpx 20rpx 15rpx -20rpx;

          .btn-task-item {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            margin-left: 30rpx;

            .u-button {
              width: 160rpx;
              height: 100%;
            }
          }
        }

        .task-item-title-wrapper {
          display: flex;
          padding: 20rpx;
          box-sizing: border-box;
          background: linear-gradient(180deg, rgba(0, 168, 139, 0.08) 0%, rgba(0, 168, 139, 0) 100%);
          border-radius: 16rpx;
          border: 8rpx solid #ffffff;

          .task-item-title {
            display: flex;
            align-items: center;
            margin-left: 20rpx;
            font-size: 32rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: $gt-primary-color;
            line-height: 45rpx;
          }
        }

        .task-item-bottom {
          display: flex;
          justify-content: right;
          align-items: flex-start;
          height: 120rpx;

          .arrow {
            margin-right: 200rpx;
          }
        }
      }

      .task-item-yszw {
        padding: 8rpx;
        margin: 0 5rpx 20rpx 5rpx;
        border-radius: 20rpx;
        border: solid 2px #eee;
        box-shadow: 0 0 3px 2px #eee;
        width: calc(100% - 28rpx);
        background-color: #fff;

        .task-item-content {
          display: flex;
          // justify-content: space-between;
          height: 100%;
          align-items: center;
          background: linear-gradient(180deg, rgba(0, 168, 139, 0.08) 0%, rgba(0, 168, 139, 0) 100%);
          position: relative;

          .task-item-l {
            width: 350rpx;
            font-size: 38rpx;
            margin-left: 30rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: $gt-primary-color;
          }

          .task-item-c {
            background-color: #e9f8f4;
            border-radius: 25rpx;
            margin-right: -60rpx;
            margin-left: auto;
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            right: 280rpx;
            z-index: 99;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .task-item-r {
            background-color: #fff;
            padding-left: 40rpx;
            margin-right: 0;
            margin-left: auto;

            .img {
              width: 200rpx;
            }
          }
        }
      }
    }

    .content-group:nth-last-child(1) {
      margin-bottom: 200rpx !important;
    }
  }
}

.empty-wrapper {
  height: calc(100% - 128px);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
