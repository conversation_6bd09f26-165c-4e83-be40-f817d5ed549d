<template>
  <view class="wrap">
    <u-navbar
      title="数据列表"
      rightIcon="plus-circle"
      @leftClick="leftClickHandler"
      @rightClick="rightClickHandler"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
    ></u-navbar>
    <view class="content">
      <view class="head-subsection" v-if="cacheable">
        <u-subsection
          class="subsection"
          :list="sectionList"
          :current="currentSection"
          @change="sectionChagneHandler"
          :activeColor="$constants.COLOR.PRIMARY_COLOR"
          fontSize="15"
        ></u-subsection>
      </view>
      <FormDataList
        ref="formDataList"
        :formUid="formUid"
        :customOptions="customOptions"
        :filter="filter"
        :formPageUrl="formPageUrl"
        v-show="currentSection !== 2"
      />
      <CacheDataList
        ref="cacheDataList"
        :formUid="formUid"
        :customOptions="customOptions"
        :formPageUrl="formPageUrl"
        v-if="cacheable"
        v-show="currentSection === 2"
      />
    </view>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import FormDataList from "@/pages_dm/components/FormDataList";
import CacheDataList from "@/pages_dm/components/CacheDataList";

import { constants } from "@/pages_dm/constants";
import formDataApi from "@/apis/formData";

const FORM_DATA_FILTER = [
  {
    filter: ["!=", "审核状态", "已退回"],
    section: "已提交",
  },
  {
    filter: ["=", "审核状态", "已退回"],
    section: "已退回",
  },
  {
    filter: null,
    section: "暂存",
  },
];

const DM_FORM_ARRAY = {
  地膜采样表2024: "采样指标值",
  地膜调查表2024: "调查指标值",
  棚膜调查表2024: "调查指标值",
  地膜采样表: "采样指标值",
  地膜调查表: "调查指标值",
  棚膜调查表: "调查指标值",
};

export default {
  mixins: [BasePage],

  components: {
    FormDataList,
    CacheDataList,
  },

  data() {
    return {
      // 表单UID
      formUid: undefined,
      cacheable: true,
      currentSection: 0,
      formPageUrl: null,
      customFields: {},
    };
  },

  async onShow() {
    this.$nextTick(async () => {
      if (this.currentSection === 2) {
        await this.$refs.cacheDataList?.loadData();
      } else {
        await this.$refs.formDataList?.reloadData();
      }
    });
  },

  onLoad(option) {
    this.formUid = option.formUid;
    this.cacheable = option.cacheable !== "false";
    this.formPageUrl = option.formPageUrl;

    const pick = ["formUid", "cacheable", "formPageUrl"];

    if (option.data) {
      this.customFields = JSON.parse(option.data);
    } else {
      this.customFields = JSON.parse(JSON.stringify(option));
    }

    Object.keys(this.customFields).forEach((item) => {
      if (pick.includes(item)) {
        delete this.customFields[item];
      }
    });
  },

  computed: {
    sectionList() {
      return FORM_DATA_FILTER.map((f) => f.section);
    },
    filter() {
      const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const { roles } = userInfo;
      let isSamplingUnit = false;
      roles.forEach((item) => {
        if (item.code === "dm_samplingunit_sampling") {
          isSamplingUnit = true;
        }
      });
      if (Object.keys(DM_FORM_ARRAY).includes(this.formUid)) {
        const result = ["and", ["=", "任务_uid", this.customFields.任务_uid], ["=", "监测点_uid", this.customFields.监测点_uid]];
        if (FORM_DATA_FILTER[this.currentSection].filter) result.push(FORM_DATA_FILTER[this.currentSection].filter);
        if (isSamplingUnit) result.push(["=", "create_by", userInfo._id]);
        return result;
      } else {
        let result = [["=", "任务_uid", this.customFields.任务_uid]];
        if (FORM_DATA_FILTER[this.currentSection].filter) result.push(FORM_DATA_FILTER[this.currentSection].filter);
        if (isSamplingUnit) result.push(["=", "create_by", userInfo._id]);
        if (result.length > 1) result.unshift("and");
        else result = result[0];
        return result;
      }
    },
    customOptions() {
      return Object.assign(this.customFields, {});
    },
  },
  watch: {},

  methods: {
    leftClickHandler() {
      this.navigateBack();
    },

    async rightClickHandler() {
      if (Object.keys(DM_FORM_ARRAY).includes(this.formUid)) {
        const total = await this.getFormDataTotal(this.formUid);
        if (total >= this.customFields[DM_FORM_ARRAY[this.formUid]]) {
          this.showError("点位指标已满");
          return;
        }
      }
      if (this.formPageUrl) {
        const pageUrl = `${constants.PAGE[this.formPageUrl]}?formUid=${this.formUid}`;

        const genUrl = this.getGenPageUrl(pageUrl);
        this.navigateTo(genUrl);
        return;
      }

      const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=${this.formUid}`;

      const genUrl = this.getGenPageUrl(pageUrl);
      this.navigateTo(genUrl);
    },

    async getFormDataTotal(formUid) {
      const { 任务_uid, 监测点_uid } = this.customFields;
      const filter = ["and", ["=", "任务_uid", 任务_uid], ["=", "监测点_uid", 监测点_uid]];
      const params = {
        filter,
        page: {
          pageNum: 1,
          pageSize: 10,
        },
      };
      const res = await formDataApi.getFormRecords(formUid, params);
      return res.total;
    },

    async sectionChagneHandler(index) {
      this.currentSection = index;
      this.clearSearchFields();
      if (index === 2) {
        await this.$refs.cacheDataList?.loadData();
      } else {
        console.log(this.filter);
      }
    },
    clearSearchFields() {
      this.$refs.formDataList.clearSearchFields();
    },

    getGenPageUrl(url) {
      // Object.keys(this.customFields).forEach((item) => {
      // 	const itemUrl = `&${item}=${this.customFields[item]}`;
      // 	url = url.concat(itemUrl);
      // });
      url = url.concat(`&data=${JSON.stringify(this.customFields)}`);
      return url;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_dm/styles/common.scss";

.wrap {
  display: flex;
  flex-direction: column;
  height: 100vh;

  /deep/ .u-navbar__content {
    .u-navbar__content__title,
    .u-icon__icon {
      color: $gt-navbar-title-color !important;
      font-size: 36rpx;
    }
  }

  /deep/ .u-subsection {
    height: 84rpx !important;
    background-color: #fff !important;

    .u-subsection--button {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  /deep/ .u-subsection--button__bar {
    width: 2em !important;
    left: calc(17.6% - 1em) !important;
    bottom: 5rpx;
    height: 7rpx !important;
    background-color: $gt-primary-color !important;
  }

  .head-subsection {
    .subsection {
      height: 80rpx;
    }
  }

  .content {
    flex: 1;
    padding: 12px;
    // display: flex;
    // flex-direction: column;
    overflow: hidden;
  }
}
</style>
