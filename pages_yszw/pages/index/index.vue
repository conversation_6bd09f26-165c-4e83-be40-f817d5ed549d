<template>
  <view class="wrapper">
    <u-navbar title="任务列表" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder @leftClick="backHome">
      <template #left>
        <u-icon name="home" size="40rpx" color="#fff"></u-icon>
      </template>
    </u-navbar>

    <view class="content">
      <view class="empty-wrapper" v-if="emptyVisible">
        <u-empty text="当前用户未分配任务, 请联系管理员" icon="/static/images/empty.png"></u-empty>
      </view>
      <template>
        <view class="content-group" v-if="showyszw">
          <view class="content-group-title">野生植物原生境保护</view>
          <view class="task-item-yszw yszw-item" @click="menuClickHandler('数据采集')">
            <view class="task-item-content">
              <view class="task-item-l"> 数据采集与审核 </view>
              <view class="task-item-c">
                <u-icon class="arrow" name="play-right-fill" color="#00a88a"></u-icon>
              </view>
              <view class="task-item-r">
                <image class="img" src="@/static/images/yszw/img1.png" mode="widthFix" />
              </view>
            </view>
          </view>
          <view class="task-item-yszw yszw-item" v-if="showyszwhj" @click="menuClickHandler('进度汇交')">
            <view class="task-item-content">
              <view class="task-item-l">进度汇交 </view>
              <view class="task-item-c">
                <u-icon class="arrow" name="play-right-fill" color="#00a88a"></u-icon>
              </view>
              <view class="task-item-r">
                <image class="img" src="@/static/images/yszw/img2.png" mode="widthFix" />
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <Tabbar :config="tabbar" :value="0"></Tabbar>
  </view>
</template>

<script>
// 权限白名单
import Tabbar from "@/components/common/Tabbar";
import { tabbar } from "@/pages_yszw/config";

import BasePage from "@/pages/BasePage";
import TabPage from "@/pages/TabPage";
import { constants } from "@/pages_yszw/constants";

import loginApi from "@/apis/login";
import formDataApi from "@/apis/formData";
import taskApi from "../../apis/task";

const ROLE_WHITE = {
  yszw: ["yszw_county_collection", "yszw_decision_analysis", "yszw_province_manage", "yszw_city_manage", "yszw_county_manage", "admin"],
  yszwhj: ["yszw_decision_analysis", "yszw_province_manage", "yszw_city_manage", "yszw_county_manage", "admin"],
};

const TASK_ITEM_INFO = [
  { label: "年份", key: "年份" },
  { label: "开始日期", key: "create_time" },
  { label: "轮次", key: "轮次" },
];

const TASK_STATUS = ["未开始", "进行中", "已结束"];

export default {
  mixins: [BasePage, TabPage],

  components: {
    Tabbar,
  },

  data() {
    return {
      taskList: [],
      roleWhite: ROLE_WHITE,
      taskItemInfo: TASK_ITEM_INFO,
      emptyVisible: false,
      menuName: null,
      showyszw: false,
      showyszwhj: false,
      tabbar,
    };
  },

  async onLoad(options) {
    this.menuName = options.menuName;
    try {
      await this.getUserInfo();
      // 请求任务表
      await this.getTaskList();
      await this.checkRoles();
      // 获取系统配置表并缓存起来
      await this.getSystemConfigForm();
    } catch (err) {
      console.log(err);
    }
  },
  // 计算属性
  computed: {},

  methods: {
    backHome() {
      uni.reLaunch({
        url: "/pages/Workspace/index",
      });
    },
    // 获取新版的用户信息
    async getUserInfo() {
      try {
        const userInfo = await loginApi.getNewUserInfo();
        await uni.setStorage({
          key: "userInfo",
          data: JSON.stringify(userInfo),
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 获取系统配置表
    async getSystemConfigForm() {
      try {
        const config = await formDataApi.getFormRecords("系统配置表");
        const obj = {};
        config.list.forEach((item) => {
          obj[item.配置项] = item.配置值;
        });
        uni.setStorageSync("systemConfig", JSON.stringify(obj));
      } catch (error) {
        console.log(error);
      }
    },

    async getTaskList() {
      const filter = ["and", ["=", "业务类型", "yszw"], ["=", "status", "进行中"]];
      const result = await taskApi.getTaskList(filter);
      if (!result) {
        return;
      }
      this.taskList = result;
      this.emptyVisible = this.taskList.length === 0;
    },

    checkRoles() {
      const userInfo = JSON.parse(uni.getStorageSync("userInfo"));
      const { roles } = userInfo;

      roles.forEach((item) => {
        if (this.roleWhite.yszw.includes(item.code)) this.showyszw = true;
        if (this.roleWhite.yszwhj.includes(item.code)) this.showyszwhj = true;
      });
    },

    getTaskButtonList() {
      return [
        {
          label: "进入",
          key: "goTo",
          disabled: false,
          icon: "edit-pen",
          type: "primary",
        },
      ];
    },

    taskClickHandler(task) {
      const { 业务类型 } = task;
      switch (业务类型) {
        case Object.keys(TASK_TYPE)[0]: {
          const { 调查表_uid, _id, 年份 } = task;
          const pageUrl = `${constants.PAGE.FORM_LIST_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
          this.navigateTo(pageUrl);
          break;
        }
        case Object.keys(TASK_TYPE)[1]: {
          this.navigatePointListHandler(task);
          break;
        }
      }
    },

    navigatePointListHandler(task, taskType = null) {
      const { 子任务表_uid, _id, 任务类型, 业务类型, 年份 } = task;
      const menuName = "野生植物原生境保护";
      let pageUrl;
      pageUrl = `${constants.PAGE.WILD_PLANT_TASK_LIST_PAGE}?子任务表_uid=${子任务表_uid}&任务_uid=${_id}&任务类型=${任务类型}&menuName=${menuName}`;
      this.navigateTo(pageUrl);
    },

    fillOutFormHandler(task, key, taskType) {
      if (!key) return;
      switch (key) {
        case "add": {
          const { status } = task;
          if (status === TASK_STATUS[1]) {
            const { 调查表_uid, _id, 年份 } = task;
            const pageUrl = `${constants.PAGE.FORM_PAGE_URL}?formUid=${调查表_uid}&任务_uid=${_id}&年份=${年份}`;
            this.navigateTo(pageUrl);
            break;
          }
        }
        case "readonly": {
          this.taskClickHandler(task);
          break;
        }
        case "goTo": {
          this.navigatePointListHandler(task, taskType);
          break;
        }
      }
    },

    menuClickHandler(e) {
      switch (e) {
        case "数据采集":
          let page = constants.PAGE.PLANTS_POINTS_LIST;
          if (this.userInfo && this.userInfo.roles) {
            this.userInfo.roles.forEach((item) => {
              if (
                item.code == "yszw_province_manage" ||
                item.code == "yszw_city_manage" ||
                item.code == "yszw_county_manage"
                // ||
                // item.code == "admin"
              ) {
                page = constants.PAGE.PLANTS_AUDIT;
              }
            });
          }
          this.navigateTo(page);
          break;
        case "进度汇交":
          this.navigateTo(constants.PAGE.PLANTS_PROGRESS);
          break;
        default:
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/pages_yszw/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
    font-size: 36rpx;
  }

  .u-navbar__content__left {
    display: none;
  }
}

.wrapper {
  position: fixed;
  width: calc(100% - 40rpx);
  height: 100vh;
  background: #f5f5f9;
  padding: 20rpx;

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 120rpx);
    overflow: auto;

    .content-group {
      .content-group-title {
        padding: 20rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #010101;
        line-height: 45rpx;
      }

      .task-item {
        padding: 8rpx;
        margin: 0 5rpx 20rpx 5rpx;
        border-radius: 20rpx;
        border: solid 1px #eee;
        box-shadow: 0 0 3px 2px #eee;
        width: calc(100% - 28rpx);
        max-height: 400rpx;
        background: #fff;
        display: flex;
        flex-direction: column;

        .task-item-info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          flex-wrap: nowrap;
          padding: 0 20rpx;
          font-weight: normal;

          .task-item-info-content {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #010101;
            line-height: 40rpx;
            padding: 18rpx;
            // margin-right: 80rpx;
          }
        }

        .task-item-operation {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          height: 64rpx;
          margin: 30rpx 20rpx 15rpx -20rpx;

          .btn-task-item {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            margin-left: 30rpx;

            .u-button {
              width: 160rpx;
              height: 100%;
            }
          }
        }

        .task-item-title-wrapper {
          display: flex;
          padding: 20rpx;
          box-sizing: border-box;
          background: linear-gradient(180deg, rgba(0, 168, 139, 0.08) 0%, rgba(0, 168, 139, 0) 100%);
          border-radius: 16rpx;
          border: 8rpx solid #ffffff;

          .task-item-title {
            display: flex;
            align-items: center;
            margin-left: 20rpx;
            font-size: 32rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: $gt-primary-color;
            line-height: 45rpx;
          }
        }

        .task-item-bottom {
          display: flex;
          justify-content: right;
          align-items: flex-start;
          height: 120rpx;

          .arrow {
            margin-right: 200rpx;
          }
        }
      }

      .task-item-yszw {
        padding: 8rpx;
        margin: 0 5rpx 20rpx 5rpx;
        border-radius: 20rpx;
        border: solid 2px #eee;
        box-shadow: 0 0 3px 2px #eee;
        width: calc(100% - 28rpx);
        background-color: #fff;

        .task-item-content {
          display: flex;
          // justify-content: space-between;
          height: 100%;
          align-items: center;
          background: linear-gradient(180deg, rgba(0, 168, 139, 0.08) 0%, rgba(0, 168, 139, 0) 100%);
          position: relative;

          .task-item-l {
            width: 350rpx;
            font-size: 38rpx;
            margin-left: 30rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: $gt-primary-color;
          }

          .task-item-c {
            background-color: #e9f8f4;
            border-radius: 25rpx;
            margin-right: -60rpx;
            margin-left: auto;
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            right: 280rpx;
            z-index: 99;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .task-item-r {
            background-color: #fff;
            padding-left: 40rpx;
            margin-right: 0;
            margin-left: auto;

            .img {
              width: 200rpx;
            }
          }
        }
      }
    }

    .content-group:nth-last-child(1) {
      margin-bottom: 200rpx !important;
    }
  }
}

.empty-wrapper {
  height: calc(100% - 128px);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
